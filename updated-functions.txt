
pub fn mdq::md_elem::elem::CodeVariant::clone(&self) -> mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::eq(&self, other: &mdq::md_elem::elem::CodeVariant) -> bool
pub fn mdq::md_elem::elem::CodeVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::CodeVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::CodeVariant::into(self) -> U
pub fn mdq::md_elem::elem::CodeVariant::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::CodeVariant::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::CodeVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::CodeVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::CodeVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::CodeVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::CodeVariant::from(t: T) -> T
pub fn mdq::md_elem::elem::ColumnAlignment::clone(&self) -> mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::cmp(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> 
core::cmp::Ordering
pub fn mdq::md_elem::elem::ColumnAlignment::eq(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> bool
pub fn mdq::md_elem::elem::ColumnAlignment::partial_cmp(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::ColumnAlignment::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::ColumnAlignment::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::ColumnAlignment::into(self) -> U
pub fn mdq::md_elem::elem::ColumnAlignment::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::ColumnAlignment::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::ColumnAlignment::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::ColumnAlignment::to_owned(&self) -> T
pub fn mdq::md_elem::elem::ColumnAlignment::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::ColumnAlignment::borrow(&self) -> &T
pub fn mdq::md_elem::elem::ColumnAlignment::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::ColumnAlignment::from(t: T) -> T
pub fn mdq::md_elem::elem::FrontMatterVariant::name(self) -> &'static str
pub fn mdq::md_elem::elem::FrontMatterVariant::separator(self) -> &'static str
pub fn mdq::md_elem::elem::FrontMatterVariant::clone(&self) -> mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> 
core::cmp::Ordering
pub fn mdq::md_elem::elem::FrontMatterVariant::eq(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> bool
pub fn mdq::md_elem::elem::FrontMatterVariant::partial_cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::FrontMatterVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::FrontMatterVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::FrontMatterVariant::into(self) -> U
pub fn mdq::md_elem::elem::FrontMatterVariant::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::FrontMatterVariant::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::FrontMatterVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatterVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::FrontMatterVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::FrontMatterVariant::from(t: T) -> T
pub fn mdq::md_elem::elem::Inline::clone(&self) -> mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::eq(&self, other: &mdq::md_elem::elem::Inline) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
pub fn mdq::md_elem::elem::Inline::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Inline::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Inline::into(self) -> U
pub fn mdq::md_elem::elem::Inline::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Inline::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Inline::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Inline::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Inline::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Inline::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Inline::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Inline::from(t: T) -> T
pub fn mdq::md_elem::elem::LinkReference::clone(&self) -> mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::cmp(&self, other: &mdq::md_elem::elem::LinkReference) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::LinkReference::eq(&self, other: &mdq::md_elem::elem::LinkReference) -> bool
pub fn mdq::md_elem::elem::LinkReference::partial_cmp(&self, other: &mdq::md_elem::elem::LinkReference) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::LinkReference::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::LinkReference::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::LinkReference::into(self) -> U
pub fn mdq::md_elem::elem::LinkReference::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::LinkReference::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::LinkReference::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::LinkReference::to_owned(&self) -> T
pub fn mdq::md_elem::elem::LinkReference::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::LinkReference::borrow(&self) -> &T
pub fn mdq::md_elem::elem::LinkReference::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::LinkReference::from(t: T) -> T
pub fn mdq::md_elem::elem::SpanVariant::clone(&self) -> mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::cmp(&self, other: &mdq::md_elem::elem::SpanVariant) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::SpanVariant::eq(&self, other: &mdq::md_elem::elem::SpanVariant) -> bool
pub fn mdq::md_elem::elem::SpanVariant::partial_cmp(&self, other: &mdq::md_elem::elem::SpanVariant) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::SpanVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::SpanVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::SpanVariant::into(self) -> U
pub fn mdq::md_elem::elem::SpanVariant::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::SpanVariant::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::SpanVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::SpanVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::SpanVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::SpanVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::SpanVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::SpanVariant::from(t: T) -> T
pub fn mdq::md_elem::elem::TextVariant::clone(&self) -> mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::cmp(&self, other: &mdq::md_elem::elem::TextVariant) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::TextVariant::eq(&self, other: &mdq::md_elem::elem::TextVariant) -> bool
pub fn mdq::md_elem::elem::TextVariant::partial_cmp(&self, other: &mdq::md_elem::elem::TextVariant) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::TextVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::TextVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::TextVariant::into(self) -> U
pub fn mdq::md_elem::elem::TextVariant::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::TextVariant::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::TextVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::TextVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::TextVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::TextVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::TextVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::TextVariant::from(t: T) -> T
pub fn mdq::md_elem::elem::BlockHtml::clone(&self) -> mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::eq(&self, other: &mdq::md_elem::elem::BlockHtml) -> bool
pub fn mdq::md_elem::elem::BlockHtml::from(value: alloc::string::String) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
pub fn mdq::md_elem::elem::BlockHtml::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::BlockHtml::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::BlockHtml::into(self) -> U
pub fn mdq::md_elem::elem::BlockHtml::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::BlockHtml::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::BlockHtml::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockHtml::to_owned(&self) -> T
pub fn mdq::md_elem::elem::BlockHtml::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::BlockHtml::borrow(&self) -> &T
pub fn mdq::md_elem::elem::BlockHtml::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::BlockHtml::from(t: T) -> T
pub fn mdq::md_elem::elem::BlockQuote::clone(&self) -> mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::eq(&self, other: &mdq::md_elem::elem::BlockQuote) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockQuote) -> Self
pub fn mdq::md_elem::elem::BlockQuote::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::BlockQuote::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::BlockQuote::into(self) -> U
pub fn mdq::md_elem::elem::BlockQuote::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::BlockQuote::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::BlockQuote::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockQuote::to_owned(&self) -> T
pub fn mdq::md_elem::elem::BlockQuote::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::BlockQuote::borrow(&self) -> &T
pub fn mdq::md_elem::elem::BlockQuote::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::BlockQuote::from(t: T) -> T
pub fn mdq::md_elem::elem::CodeBlock::clone(&self) -> mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::eq(&self, other: &mdq::md_elem::elem::CodeBlock) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::CodeBlock) -> Self
pub fn mdq::md_elem::elem::CodeBlock::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::CodeBlock::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::CodeBlock::into(self) -> U
pub fn mdq::md_elem::elem::CodeBlock::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::CodeBlock::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::CodeBlock::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeBlock::to_owned(&self) -> T
pub fn mdq::md_elem::elem::CodeBlock::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::CodeBlock::borrow(&self) -> &T
pub fn mdq::md_elem::elem::CodeBlock::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::CodeBlock::from(t: T) -> T
pub fn mdq::md_elem::elem::CodeOpts::clone(&self) -> mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::eq(&self, other: &mdq::md_elem::elem::CodeOpts) -> bool
pub fn mdq::md_elem::elem::CodeOpts::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::CodeOpts::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::CodeOpts::into(self) -> U
pub fn mdq::md_elem::elem::CodeOpts::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::CodeOpts::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::CodeOpts::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeOpts::to_owned(&self) -> T
pub fn mdq::md_elem::elem::CodeOpts::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::CodeOpts::borrow(&self) -> &T
pub fn mdq::md_elem::elem::CodeOpts::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::CodeOpts::from(t: T) -> T
pub fn mdq::md_elem::elem::FootnoteId::as_str(&self) -> &str
pub fn mdq::md_elem::elem::FootnoteId::clone(&self) -> mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::eq(&self, other: &mdq::md_elem::elem::FootnoteId) -> bool
pub fn mdq::md_elem::elem::FootnoteId::from(id: alloc::string::String) -> Self
pub fn mdq::md_elem::elem::FootnoteId::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::FootnoteId::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::FootnoteId::into(self) -> U
pub fn mdq::md_elem::elem::FootnoteId::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::FootnoteId::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::FootnoteId::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FootnoteId::to_owned(&self) -> T
pub fn mdq::md_elem::elem::FootnoteId::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::FootnoteId::borrow(&self) -> &T
pub fn mdq::md_elem::elem::FootnoteId::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::FootnoteId::from(t: T) -> T
pub fn mdq::md_elem::elem::FrontMatter::clone(&self) -> mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::eq(&self, other: &mdq::md_elem::elem::FrontMatter) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
pub fn mdq::md_elem::elem::FrontMatter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::FrontMatter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::FrontMatter::into(self) -> U
pub fn mdq::md_elem::elem::FrontMatter::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::FrontMatter::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::FrontMatter::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatter::to_owned(&self) -> T
pub fn mdq::md_elem::elem::FrontMatter::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::FrontMatter::borrow(&self) -> &T
pub fn mdq::md_elem::elem::FrontMatter::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::FrontMatter::from(t: T) -> T
pub fn mdq::md_elem::elem::Image::clone(&self) -> mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::eq(&self, other: &mdq::md_elem::elem::Image) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Image) -> Self
pub fn mdq::md_elem::elem::Image::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Image::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Image::into(self) -> U
pub fn mdq::md_elem::elem::Image::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Image::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Image::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Image::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Image::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Image::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Image::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Image::from(t: T) -> T
pub fn mdq::md_elem::elem::Link::clone(&self) -> mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::eq(&self, other: &mdq::md_elem::elem::Link) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Link) -> Self
pub fn mdq::md_elem::elem::Link::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Link::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Link::into(self) -> U
pub fn mdq::md_elem::elem::Link::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Link::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Link::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Link::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Link::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Link::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Link::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Link::from(t: T) -> T
pub fn mdq::md_elem::elem::LinkDefinition::clone(&self) -> mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::eq(&self, other: &mdq::md_elem::elem::LinkDefinition) -> bool
pub fn mdq::md_elem::elem::LinkDefinition::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::LinkDefinition::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::LinkDefinition::into(self) -> U
pub fn mdq::md_elem::elem::LinkDefinition::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::LinkDefinition::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::LinkDefinition::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::LinkDefinition::to_owned(&self) -> T
pub fn mdq::md_elem::elem::LinkDefinition::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::LinkDefinition::borrow(&self) -> &T
pub fn mdq::md_elem::elem::LinkDefinition::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::LinkDefinition::from(t: T) -> T
pub fn mdq::md_elem::elem::List::clone(&self) -> mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::eq(&self, other: &mdq::md_elem::elem::List) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::List) -> Self
pub fn mdq::md_elem::elem::List::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::List::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::List::into(self) -> U
pub fn mdq::md_elem::elem::List::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::List::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::List::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::List::to_owned(&self) -> T
pub fn mdq::md_elem::elem::List::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::List::borrow(&self) -> &T
pub fn mdq::md_elem::elem::List::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::List::from(t: T) -> T
pub fn mdq::md_elem::elem::ListItem::clone(&self) -> mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::eq(&self, other: &mdq::md_elem::elem::ListItem) -> bool
pub fn mdq::md_elem::elem::ListItem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::ListItem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::ListItem::into(self) -> U
pub fn mdq::md_elem::elem::ListItem::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::ListItem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::ListItem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::ListItem::to_owned(&self) -> T
pub fn mdq::md_elem::elem::ListItem::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::ListItem::borrow(&self) -> &T
pub fn mdq::md_elem::elem::ListItem::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::ListItem::from(t: T) -> T
pub fn mdq::md_elem::elem::Paragraph::clone(&self) -> mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::eq(&self, other: &mdq::md_elem::elem::Paragraph) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Paragraph) -> Self
pub fn mdq::md_elem::elem::Paragraph::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Paragraph::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Paragraph::into(self) -> U
pub fn mdq::md_elem::elem::Paragraph::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Paragraph::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Paragraph::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Paragraph::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Paragraph::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Paragraph::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Paragraph::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Paragraph::from(t: T) -> T
pub fn mdq::md_elem::elem::Section::clone(&self) -> mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::eq(&self, other: &mdq::md_elem::elem::Section) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Section) -> Self
pub fn mdq::md_elem::elem::Section::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Section::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Section::into(self) -> U
pub fn mdq::md_elem::elem::Section::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Section::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Section::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Section::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Section::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Section::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Section::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Section::from(t: T) -> T
pub fn mdq::md_elem::elem::Span::clone(&self) -> mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::eq(&self, other: &mdq::md_elem::elem::Span) -> bool
pub fn mdq::md_elem::elem::Span::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Span::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Span::into(self) -> U
pub fn mdq::md_elem::elem::Span::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Span::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Span::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Span::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Span::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Span::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Span::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Span::from(t: T) -> T
pub fn mdq::md_elem::elem::Table::clone(&self) -> mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::eq(&self, other: &mdq::md_elem::elem::Table) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Table) -> Self
pub fn mdq::md_elem::elem::Table::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Table::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Table::into(self) -> U
pub fn mdq::md_elem::elem::Table::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Table::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Table::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Table::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Table::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Table::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Table::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Table::from(t: T) -> T
pub fn mdq::md_elem::elem::Text::clone(&self) -> mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::eq(&self, other: &mdq::md_elem::elem::Text) -> bool
pub fn mdq::md_elem::elem::Text::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Text::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Text::into(self) -> U
pub fn mdq::md_elem::elem::Text::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::elem::Text::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::elem::Text::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Text::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Text::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Text::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Text::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Text::from(t: T) -> T
pub fn mdq::md_elem::InvalidMd::eq(&self, other: &mdq::md_elem::InvalidMd) -> bool
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::InvalidMd::into(self) -> U
pub fn mdq::md_elem::InvalidMd::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::InvalidMd::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::InvalidMd::to_string(&self) -> alloc::string::String
pub fn mdq::md_elem::InvalidMd::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::InvalidMd::borrow(&self) -> &T
pub fn mdq::md_elem::InvalidMd::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::InvalidMd::from(t: T) -> T
pub fn mdq::md_elem::MdElem::clone(&self) -> mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::eq(&self, other: &mdq::md_elem::MdElem) -> bool
pub fn mdq::md_elem::MdElem::from(elems: alloc::vec::Vec<mdq::md_elem::MdElem>) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockQuote) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::CodeBlock) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Image) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Link) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::List) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Paragraph) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Section) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Table) -> Self
pub fn mdq::md_elem::MdElem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MdElem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::MdElem::into(self) -> U
pub fn mdq::md_elem::MdElem::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::MdElem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::MdElem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdElem::to_owned(&self) -> T
pub fn mdq::md_elem::MdElem::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MdElem::borrow(&self) -> &T
pub fn mdq::md_elem::MdElem::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MdElem::from(t: T) -> T
pub fn mdq::md_elem::MarkdownPart::clone(&self) -> mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::eq(&self, other: &mdq::md_elem::MarkdownPart) -> bool
pub fn mdq::md_elem::MarkdownPart::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MarkdownPart::into(self) -> U
pub fn mdq::md_elem::MarkdownPart::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::MarkdownPart::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::MarkdownPart::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MarkdownPart::to_owned(&self) -> T
pub fn mdq::md_elem::MarkdownPart::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MarkdownPart::borrow(&self) -> &T
pub fn mdq::md_elem::MarkdownPart::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MarkdownPart::from(t: T) -> T
pub fn mdq::md_elem::MdContext::get_footnote(&self, footnote_id: &mdq::md_elem::elem::FootnoteId) -> 
&alloc::vec::Vec<mdq::md_elem::MdElem>
pub fn mdq::md_elem::MdContext::clone(&self) -> mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::eq(&self, other: &mdq::md_elem::MdContext) -> bool
pub fn mdq::md_elem::MdContext::default() -> mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MdContext::into(self) -> U
pub fn mdq::md_elem::MdContext::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::MdContext::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::MdContext::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdContext::to_owned(&self) -> T
pub fn mdq::md_elem::MdContext::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MdContext::borrow(&self) -> &T
pub fn mdq::md_elem::MdContext::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MdContext::from(t: T) -> T
pub fn mdq::md_elem::MdDoc::parse(text: &str, options: &mdq::md_elem::ParseOptions) -> core::result::Result<Self, 
mdq::md_elem::InvalidMd>
pub fn mdq::md_elem::MdDoc::clone(&self) -> mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::eq(&self, other: &mdq::md_elem::MdDoc) -> bool
pub fn mdq::md_elem::MdDoc::default() -> mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MdDoc::into(self) -> U
pub fn mdq::md_elem::MdDoc::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::MdDoc::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::MdDoc::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdDoc::to_owned(&self) -> T
pub fn mdq::md_elem::MdDoc::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MdDoc::borrow(&self) -> &T
pub fn mdq::md_elem::MdDoc::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MdDoc::from(t: T) -> T
pub fn mdq::md_elem::ParseOptions::gfm() -> Self
pub fn mdq::md_elem::ParseOptions::default() -> Self
pub fn mdq::md_elem::ParseOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::ParseOptions::into(self) -> U
pub fn mdq::md_elem::ParseOptions::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::ParseOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::ParseOptions::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::ParseOptions::borrow(&self) -> &T
pub fn mdq::md_elem::ParseOptions::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::ParseOptions::from(t: T) -> T
pub fn mdq::md_elem::UnknownMdParseError::eq(&self, _other: &Self) -> bool
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::UnknownMdParseError::into(self) -> U
pub fn mdq::md_elem::UnknownMdParseError::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::md_elem::UnknownMdParseError::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::UnknownMdParseError::to_string(&self) -> alloc::string::String
pub fn mdq::md_elem::UnknownMdParseError::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::UnknownMdParseError::borrow(&self) -> &T
pub fn mdq::md_elem::UnknownMdParseError::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::UnknownMdParseError::from(t: T) -> T
pub fn mdq::select::ListItemTask::clone(&self) -> mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::cmp(&self, other: &mdq::select::ListItemTask) -> core::cmp::Ordering
pub fn mdq::select::ListItemTask::eq(&self, other: &mdq::select::ListItemTask) -> bool
pub fn mdq::select::ListItemTask::partial_cmp(&self, other: &mdq::select::ListItemTask) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::ListItemTask::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ListItemTask::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ListItemTask::into(self) -> U
pub fn mdq::select::ListItemTask::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::ListItemTask::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::ListItemTask::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemTask::to_owned(&self) -> T
pub fn mdq::select::ListItemTask::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ListItemTask::borrow(&self) -> &T
pub fn mdq::select::ListItemTask::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ListItemTask::from(t: T) -> T
pub fn mdq::select::Matcher::clone(&self) -> mdq::select::Matcher
pub fn mdq::select::Matcher::cmp(&self, other: &mdq::select::Matcher) -> core::cmp::Ordering
pub fn mdq::select::Matcher::eq(&self, other: &mdq::select::Matcher) -> bool
pub fn mdq::select::Matcher::partial_cmp(&self, other: &mdq::select::Matcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::Matcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::Matcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::Matcher::into(self) -> U
pub fn mdq::select::Matcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::Matcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::Matcher::clone_into(&self, target: &mut T)
pub fn mdq::select::Matcher::to_owned(&self) -> T
pub fn mdq::select::Matcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::Matcher::borrow(&self) -> &T
pub fn mdq::select::Matcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::Matcher::from(t: T) -> T
pub fn mdq::select::Selector::find_nodes(self, doc: mdq::md_elem::MdDoc) -> 
mdq::select::Result<(alloc::vec::Vec<mdq::md_elem::MdElem>, mdq::md_elem::MdContext)>
pub fn mdq::select::Selector::clone(&self) -> mdq::select::Selector
pub fn mdq::select::Selector::cmp(&self, other: &mdq::select::Selector) -> core::cmp::Ordering
pub fn mdq::select::Selector::eq(&self, other: &mdq::select::Selector) -> bool
pub fn mdq::select::Selector::partial_cmp(&self, other: &mdq::select::Selector) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::Selector::try_from(value: &alloc::string::String) -> core::result::Result<Self, Self::Error>
pub fn mdq::select::Selector::try_from(value: &str) -> core::result::Result<Self, Self::Error>
pub fn mdq::select::Selector::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::Selector::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::Selector::into(self) -> U
pub fn mdq::select::Selector::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::Selector::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::Selector::clone_into(&self, target: &mut T)
pub fn mdq::select::Selector::to_owned(&self) -> T
pub fn mdq::select::Selector::type_id(&self) -> core::any::TypeId
pub fn mdq::select::Selector::borrow(&self) -> &T
pub fn mdq::select::Selector::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::Selector::from(t: T) -> T
pub fn mdq::select::BlockQuoteMatcher::clone(&self) -> mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::cmp::Ordering
pub fn mdq::select::BlockQuoteMatcher::eq(&self, other: &mdq::select::BlockQuoteMatcher) -> bool
pub fn mdq::select::BlockQuoteMatcher::partial_cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::BlockQuoteMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::BlockQuoteMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::BlockQuoteMatcher::into(self) -> U
pub fn mdq::select::BlockQuoteMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::BlockQuoteMatcher::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::select::BlockQuoteMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::BlockQuoteMatcher::to_owned(&self) -> T
pub fn mdq::select::BlockQuoteMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::BlockQuoteMatcher::borrow(&self) -> &T
pub fn mdq::select::BlockQuoteMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::BlockQuoteMatcher::from(t: T) -> T
pub fn mdq::select::CodeBlockMatcher::clone(&self) -> mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::cmp::Ordering
pub fn mdq::select::CodeBlockMatcher::eq(&self, other: &mdq::select::CodeBlockMatcher) -> bool
pub fn mdq::select::CodeBlockMatcher::partial_cmp(&self, other: &mdq::select::CodeBlockMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::CodeBlockMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::CodeBlockMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::CodeBlockMatcher::into(self) -> U
pub fn mdq::select::CodeBlockMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::CodeBlockMatcher::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::select::CodeBlockMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::CodeBlockMatcher::to_owned(&self) -> T
pub fn mdq::select::CodeBlockMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::CodeBlockMatcher::borrow(&self) -> &T
pub fn mdq::select::CodeBlockMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::CodeBlockMatcher::from(t: T) -> T
pub fn mdq::select::FrontMatterMatcher::clone(&self) -> mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::cmp::Ordering
pub fn mdq::select::FrontMatterMatcher::eq(&self, other: &mdq::select::FrontMatterMatcher) -> bool
pub fn mdq::select::FrontMatterMatcher::partial_cmp(&self, other: &mdq::select::FrontMatterMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::FrontMatterMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::FrontMatterMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::FrontMatterMatcher::into(self) -> U
pub fn mdq::select::FrontMatterMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::FrontMatterMatcher::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::select::FrontMatterMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::FrontMatterMatcher::to_owned(&self) -> T
pub fn mdq::select::FrontMatterMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::FrontMatterMatcher::borrow(&self) -> &T
pub fn mdq::select::FrontMatterMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::FrontMatterMatcher::from(t: T) -> T
pub fn mdq::select::HtmlMatcher::clone(&self) -> mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::cmp(&self, other: &mdq::select::HtmlMatcher) -> core::cmp::Ordering
pub fn mdq::select::HtmlMatcher::eq(&self, other: &mdq::select::HtmlMatcher) -> bool
pub fn mdq::select::HtmlMatcher::partial_cmp(&self, other: &mdq::select::HtmlMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::HtmlMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::HtmlMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::HtmlMatcher::into(self) -> U
pub fn mdq::select::HtmlMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::HtmlMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::HtmlMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::HtmlMatcher::to_owned(&self) -> T
pub fn mdq::select::HtmlMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::HtmlMatcher::borrow(&self) -> &T
pub fn mdq::select::HtmlMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::HtmlMatcher::from(t: T) -> T
pub fn mdq::select::LinklikeMatcher::clone(&self) -> mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::cmp::Ordering
pub fn mdq::select::LinklikeMatcher::eq(&self, other: &mdq::select::LinklikeMatcher) -> bool
pub fn mdq::select::LinklikeMatcher::partial_cmp(&self, other: &mdq::select::LinklikeMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::LinklikeMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::LinklikeMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::LinklikeMatcher::into(self) -> U
pub fn mdq::select::LinklikeMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::LinklikeMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::LinklikeMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::LinklikeMatcher::to_owned(&self) -> T
pub fn mdq::select::LinklikeMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::LinklikeMatcher::borrow(&self) -> &T
pub fn mdq::select::LinklikeMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::LinklikeMatcher::from(t: T) -> T
pub fn mdq::select::ListItemMatcher::clone(&self) -> mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::cmp(&self, other: &mdq::select::ListItemMatcher) -> core::cmp::Ordering
pub fn mdq::select::ListItemMatcher::eq(&self, other: &mdq::select::ListItemMatcher) -> bool
pub fn mdq::select::ListItemMatcher::partial_cmp(&self, other: &mdq::select::ListItemMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::ListItemMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ListItemMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ListItemMatcher::into(self) -> U
pub fn mdq::select::ListItemMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::ListItemMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::ListItemMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemMatcher::to_owned(&self) -> T
pub fn mdq::select::ListItemMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ListItemMatcher::borrow(&self) -> &T
pub fn mdq::select::ListItemMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ListItemMatcher::from(t: T) -> T
pub fn mdq::select::MatchReplace::clone(&self) -> mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::cmp(&self, other: &mdq::select::MatchReplace) -> core::cmp::Ordering
pub fn mdq::select::MatchReplace::eq(&self, other: &mdq::select::MatchReplace) -> bool
pub fn mdq::select::MatchReplace::partial_cmp(&self, other: &mdq::select::MatchReplace) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::MatchReplace::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::MatchReplace::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::MatchReplace::into(self) -> U
pub fn mdq::select::MatchReplace::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::MatchReplace::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::MatchReplace::clone_into(&self, target: &mut T)
pub fn mdq::select::MatchReplace::to_owned(&self) -> T
pub fn mdq::select::MatchReplace::type_id(&self) -> core::any::TypeId
pub fn mdq::select::MatchReplace::borrow(&self) -> &T
pub fn mdq::select::MatchReplace::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::MatchReplace::from(t: T) -> T
pub fn mdq::select::ParagraphMatcher::clone(&self) -> mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::cmp::Ordering
pub fn mdq::select::ParagraphMatcher::eq(&self, other: &mdq::select::ParagraphMatcher) -> bool
pub fn mdq::select::ParagraphMatcher::partial_cmp(&self, other: &mdq::select::ParagraphMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::ParagraphMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ParagraphMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ParagraphMatcher::into(self) -> U
pub fn mdq::select::ParagraphMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::ParagraphMatcher::try_into(self) -> core::result::Result<U, <U as 
core::convert::TryFrom<T>>::Error>
pub fn mdq::select::ParagraphMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ParagraphMatcher::to_owned(&self) -> T
pub fn mdq::select::ParagraphMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ParagraphMatcher::borrow(&self) -> &T
pub fn mdq::select::ParagraphMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ParagraphMatcher::from(t: T) -> T
pub fn mdq::select::ParseError::clone(&self) -> mdq::select::ParseError
pub fn mdq::select::ParseError::eq(&self, other: &mdq::select::ParseError) -> bool
pub fn mdq::select::ParseError::source(&self) -> core::option::Option<&(dyn core::error::Error + 'static)>
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ParseError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ParseError::into(self) -> U
pub fn mdq::select::ParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::ParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::ParseError::clone_into(&self, target: &mut T)
pub fn mdq::select::ParseError::to_owned(&self) -> T
pub fn mdq::select::ParseError::to_string(&self) -> alloc::string::String
pub fn mdq::select::ParseError::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ParseError::borrow(&self) -> &T
pub fn mdq::select::ParseError::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ParseError::from(t: T) -> T
pub fn mdq::select::Regex::clone(&self) -> mdq::select::Regex
pub fn mdq::select::Regex::cmp(&self, other: &Self) -> core::cmp::Ordering
pub fn mdq::select::Regex::eq(&self, other: &Self) -> bool
pub fn mdq::select::Regex::partial_cmp(&self, other: &Self) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::Regex::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::Regex::hash<H: core::hash::Hasher>(&self, state: &mut H)
pub fn mdq::select::Regex::into(self) -> U
pub fn mdq::select::Regex::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::Regex::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::Regex::clone_into(&self, target: &mut T)
pub fn mdq::select::Regex::to_owned(&self) -> T
pub fn mdq::select::Regex::type_id(&self) -> core::any::TypeId
pub fn mdq::select::Regex::borrow(&self) -> &T
pub fn mdq::select::Regex::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::Regex::from(t: T) -> T
pub fn mdq::select::SectionMatcher::clone(&self) -> mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::cmp(&self, other: &mdq::select::SectionMatcher) -> core::cmp::Ordering
pub fn mdq::select::SectionMatcher::eq(&self, other: &mdq::select::SectionMatcher) -> bool
pub fn mdq::select::SectionMatcher::partial_cmp(&self, other: &mdq::select::SectionMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::SectionMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SectionMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::SectionMatcher::into(self) -> U
pub fn mdq::select::SectionMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::SectionMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::SectionMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::SectionMatcher::to_owned(&self) -> T
pub fn mdq::select::SectionMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::SectionMatcher::borrow(&self) -> &T
pub fn mdq::select::SectionMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::SectionMatcher::from(t: T) -> T
pub fn mdq::select::SelectError::clone(&self) -> mdq::select::SelectError
pub fn mdq::select::SelectError::clone(&self) -> mdq::select::SelectError
pub fn mdq::select::SelectError::eq(&self, other: &mdq::select::SelectError) -> bool
pub fn mdq::select::SelectError::eq(&self, other: &mdq::select::SelectError) -> bool
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::SelectError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::SelectError::into(self) -> U
pub fn mdq::select::SelectError::into(self) -> U
pub fn mdq::select::SelectError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::SelectError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::SelectError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::SelectError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::SelectError::clone_into(&self, target: &mut T)
pub fn mdq::select::SelectError::clone_into(&self, target: &mut T)
pub fn mdq::select::SelectError::to_owned(&self) -> T
pub fn mdq::select::SelectError::to_owned(&self) -> T
pub fn mdq::select::SelectError::to_string(&self) -> alloc::string::String
pub fn mdq::select::SelectError::to_string(&self) -> alloc::string::String
pub fn mdq::select::SelectError::type_id(&self) -> core::any::TypeId
pub fn mdq::select::SelectError::type_id(&self) -> core::any::TypeId
pub fn mdq::select::SelectError::borrow(&self) -> &T
pub fn mdq::select::SelectError::borrow(&self) -> &T
pub fn mdq::select::SelectError::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::SelectError::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::SelectError::from(t: T) -> T
pub fn mdq::select::SelectError::from(t: T) -> T
pub fn mdq::select::TableMatcher::clone(&self) -> mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::cmp(&self, other: &mdq::select::TableMatcher) -> core::cmp::Ordering
pub fn mdq::select::TableMatcher::eq(&self, other: &mdq::select::TableMatcher) -> bool
pub fn mdq::select::TableMatcher::partial_cmp(&self, other: &mdq::select::TableMatcher) -> 
core::option::Option<core::cmp::Ordering>
pub fn mdq::select::TableMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::TableMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::TableMatcher::into(self) -> U
pub fn mdq::select::TableMatcher::try_from(value: U) -> core::result::Result<T, <T as 
core::convert::TryFrom<U>>::Error>
pub fn mdq::select::TableMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::TableMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::TableMatcher::to_owned(&self) -> T
pub fn mdq::select::TableMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::TableMatcher::borrow(&self) -> &T
pub fn mdq::select::TableMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::TableMatcher::from(t: T) -> T


